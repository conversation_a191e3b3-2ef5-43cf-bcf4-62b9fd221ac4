{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Liver Disease Prediction - AI Assessment 2\n", "## Student: [Your Name]\n", "## Course: Introduction to Artificial Intelligence\n", "\n", "This notebook demonstrates machine learning for liver disease prediction using the required Assessment 2 criteria."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Required Libraries"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully!\n"]}], "source": ["# Data manipulation and analysis\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# Data visualization\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Machine learning\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.svm import SVC\n", "\n", "# Evaluation metrics\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Load and Explore Dataset (200+ rows, 4+ features)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Shape: (583, 11)\n", "\n", "Dataset Info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 583 entries, 0 to 582\n", "Data columns (total 11 columns):\n", " #   Column                      Non-Null Count  Dtype  \n", "---  ------                      --------------  -----  \n", " 0   Age                         583 non-null    int64  \n", " 1   Gender                      583 non-null    object \n", " 2   Total_Bilirubin             583 non-null    float64\n", " 3   Direct_Bilirubin            583 non-null    float64\n", " 4   Alkaline_Phosphotase        583 non-null    int64  \n", " 5   Alamine_Aminotransferase    583 non-null    int64  \n", " 6   Aspartate_Aminotransferase  583 non-null    int64  \n", " 7   Total_Proteins              583 non-null    float64\n", " 8   Albumin                     583 non-null    float64\n", " 9   Albumin_and_Globulin_Ratio  579 non-null    float64\n", " 10  Dataset                     583 non-null    int64  \n", "dtypes: float64(5), int64(5), object(1)\n", "memory usage: 50.2+ KB\n", "\n", "First 5 rows:\n", "   Age  Gender  Total_Bilirubin  Direct_Bilirubin  Alkaline_Phosphotase  \\\n", "0   65  Female              0.7               0.1                   187   \n", "1   62    Male             10.9               5.5                   699   \n", "2   62    Male              7.3               4.1                   490   \n", "3   58    Male              1.0               0.4                   182   \n", "4   72    Male              3.9               2.0                   195   \n", "\n", "   Alamine_Aminotransferase  Aspartate_Aminotransferase  Total_Proteins  \\\n", "0                        16                          18             6.8   \n", "1                        64                         100             7.5   \n", "2                        60                          68             7.0   \n", "3                        14                          20             6.8   \n", "4                        27                          59             7.3   \n", "\n", "   Albumin  Albumin_and_Globulin_Ratio  Dataset  \n", "0      3.3                        0.90        1  \n", "1      3.2                        0.74        1  \n", "2      3.3                        0.89        1  \n", "3      3.4                        1.00        1  \n", "4      2.4                        0.40        1  \n", "\n", "Basic Statistics:\n", "              Age  Total_Bilirubin  Direct_Bilirubin  Alkaline_Phosphotase  \\\n", "count  583.000000       583.000000        583.000000            583.000000   \n", "mean    44.746141         3.298799          1.486106            290.576329   \n", "std     16.189833         6.209522          2.808498            242.937989   \n", "min      4.000000         0.400000          0.100000             63.000000   \n", "25%     33.000000         0.800000          0.200000            175.500000   \n", "50%     45.000000         1.000000          0.300000            208.000000   \n", "75%     58.000000         2.600000          1.300000            298.000000   \n", "max     90.000000        75.000000         19.700000           2110.000000   \n", "\n", "       Alamine_Aminotransferase  Aspartate_Aminotransferase  Total_Proteins  \\\n", "count                583.000000                  583.000000      583.000000   \n", "mean                  80.713551                  109.910806        6.483190   \n", "std                  182.620356                  288.918529        1.085451   \n", "min                   10.000000                   10.000000        2.700000   \n", "25%                   23.000000                   25.000000        5.800000   \n", "50%                   35.000000                   42.000000        6.600000   \n", "75%                   60.500000                   87.000000        7.200000   \n", "max                 2000.000000                 4929.000000        9.600000   \n", "\n", "          Albumin  Albumin_and_Globulin_Ratio     Dataset  \n", "count  583.000000                  579.000000  583.000000  \n", "mean     3.141852                    0.947064    1.286449  \n", "std      0.795519                    0.319592    0.452490  \n", "min      0.900000                    0.300000    1.000000  \n", "25%      2.600000                    0.700000    1.000000  \n", "50%      3.100000                    0.930000    1.000000  \n", "75%      3.800000                    1.100000    2.000000  \n", "max      5.500000                    2.800000    2.000000  \n", "\n", "Missing Values:\n", "Age                           0\n", "Gender                        0\n", "Total_Bilirubin               0\n", "Direct_Bilirubin              0\n", "Alkaline_Phosphotase          0\n", "Alamine_Aminotransferase      0\n", "Aspartate_Aminotransferase    0\n", "Total_Proteins                0\n", "Albumin                       0\n", "Albumin_and_Globulin_Ratio    4\n", "Dataset                       0\n", "dtype: int64\n", "\n", "Data Types:\n", "Age                             int64\n", "Gender                         object\n", "Total_Bilirubin               float64\n", "Direct_Bilirubin              float64\n", "Alkaline_Phosphotase            int64\n", "Alamine_Aminotransferase        int64\n", "Aspartate_Aminotransferase      int64\n", "Total_Proteins                float64\n", "Albumin                       float64\n", "Albumin_and_Globulin_Ratio    float64\n", "Dataset                         int64\n", "dtype: object\n", "\n", "Column Names:\n", "['Age', 'Gender', 'Total_Bilirubin', 'Direct_Bilirubin', 'Alkaline_Phosphotase', 'Alamine_Aminotransferase', 'Aspartate_Aminotransferase', 'Total_Proteins', 'Albumin', 'Albumin_and_Globulin_Ratio', 'Dataset']\n"]}], "source": ["# Load dataset\n", "df = pd.read_csv('ILPD.csv', header=None)\n", "\n", "# Assign column names\n", "df.columns = ['Age', 'Gender', 'Total_Bilirubin', 'Direct_Bilirubin',\n", "              'Alkaline_Phosphotase', 'Alamine_Aminotransferase', 'Aspartate_Aminotransferase',\n", "              'Total_Proteins', 'Albumin', 'Albumin_and_Globulin_Ratio', 'Dataset']\n", "\n", "# Dataset description methods as required\n", "print(\"Dataset Shape:\", df.shape)\n", "print(\"\\nDataset Info:\")\n", "df.info()\n", "print(\"\\nFirst 5 rows:\")\n", "print(df.head())\n", "print(\"\\nBasic Statistics:\")\n", "print(df.describe())\n", "print(\"\\nMissing Values:\")\n", "print(df.isnull().sum())\n", "print(\"\\nData Types:\")\n", "print(df.dtypes)\n", "print(\"\\nColumn Names:\")\n", "print(df.columns.tolist())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Preprocessing (<PERSON><PERSON> missing values, encoding, normalization)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data preprocessing completed!\n", "Missing values after cleaning: 0\n", "Target variable distribution:\n", "Dataset\n", "1    416\n", "0    167\n", "Name: count, dtype: int64\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_5868\\322519427.py:3: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean['Albumin_and_Globulin_Ratio'].fillna(df_clean['Albumin_and_Globulin_Ratio'].median(), inplace=True)\n"]}], "source": ["# Handle missing values\n", "df_clean = df.copy()\n", "df_clean['Albumin_and_Globulin_Ratio'].fillna(df_clean['Albumin_and_Globulin_Ratio'].median(), inplace=True)\n", "\n", "# Categorical encoding (one-hot encoding for Gender)\n", "df_clean['Gender'] = df_clean['Gender'].map({'Male': 1, 'Female': 0})\n", "\n", "# Convert target variable\n", "df_clean['Dataset'] = df_clean['Dataset'].map({1: 1, 2: 0})  # 1=Disease, 0=No Disease\n", "\n", "print(\"Data preprocessing completed!\")\n", "print(\"Missing values after cleaning:\", df_clean.isnull().sum().sum())\n", "print(\"Target variable distribution:\")\n", "print(df_clean['Dataset'].value_counts())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Visualization (Box plot, Histogram, Line plot, <PERSON><PERSON><PERSON> plot)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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****************************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", "text/plain": ["<Figure size 1500x1000 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create required visualizations\n", "plt.figure(figsize=(15, 10))\n", "\n", "# 1. Box plot\n", "plt.subplot(2, 3, 1)\n", "df_clean.boxplot(column='Age', by='Dataset', ax=plt.gca())\n", "plt.title('Box Plot: Age by Disease Status')\n", "plt.suptitle('')\n", "\n", "# 2. Histo<PERSON>\n", "plt.subplot(2, 3, 2)\n", "plt.hist(df_clean['Age'], bins=20, alpha=0.7, color='skyblue')\n", "plt.title('Histogram: Age Distribution')\n", "plt.xlabel('Age')\n", "plt.ylabel('Frequency')\n", "\n", "# 3. Line plot\n", "plt.subplot(2, 3, 3)\n", "age_disease = df_clean.groupby('Age')['Dataset'].mean()\n", "plt.plot(age_disease.index, age_disease.values, marker='o')\n", "plt.title('Line Plot: Disease Rate by Age')\n", "plt.xlabel('Age')\n", "plt.ylabel('Disease Rate')\n", "\n", "# 4. <PERSON><PERSON><PERSON> plot\n", "plt.subplot(2, 3, 4)\n", "colors = ['blue' if x == 0 else 'red' for x in df_clean['Dataset']]\n", "plt.scatter(df_clean['Age'], df_clean['Total_Bilirubin'], c=colors, alpha=0.6)\n", "plt.title('Scatter Plot: Age vs Total Bilirubin')\n", "plt.xlabel('Age')\n", "plt.ylabel('Total Bilirubin')\n", "\n", "# 5. Bar plot for target variable\n", "plt.subplot(2, 3, 5)\n", "df_clean['Dataset'].value_counts().plot(kind='bar', color=['lightblue', 'lightcoral'])\n", "plt.title('Target Variable Distribution')\n", "plt.xlabel('Disease Status (0=No Disease, 1=Disease)')\n", "plt.ylabel('Count')\n", "plt.xticks(rotation=0)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Data Split and Normalization"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training set size: (466, 10)\n", "Test set size: (117, 10)\n", "Data normalization completed!\n"]}], "source": ["# Prepare features and target\n", "X = df_clean.drop('Dataset', axis=1)\n", "y = df_clean['Dataset']\n", "\n", "# Split data (80/20)\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)\n", "\n", "# Data normalization\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(f\"Training set size: {X_train.shape}\")\n", "print(f\"Test set size: {X_test.shape}\")\n", "print(\"Data normalization completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Model Implementation (Classification: 3 algorithms)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Logistic Regression Results:\n", "Accuracy: 0.735\n", "Precision: 0.741\n", "Recall: 0.964\n", "F1-Score: 0.838\n", "\n", "Random Forest Results:\n", "Accuracy: 0.735\n", "Precision: 0.765\n", "Recall: 0.904\n", "F1-Score: 0.829\n", "\n", "Support Vector Machine Results:\n", "Accuracy: 0.709\n", "Precision: 0.709\n", "Recall: 1.000\n", "F1-Score: 0.830\n"]}], "source": ["# Initialize models\n", "models = {\n", "    'Logistic Regression': LogisticRegression(random_state=42),\n", "    'Random Forest': RandomForestClassifier(random_state=42, n_estimators=100),\n", "    'Support Vector Machine': SVC(random_state=42)\n", "}\n", "\n", "# Train models and store results\n", "results = {}\n", "predictions = {}\n", "\n", "for name, model in models.items():\n", "    # Train model\n", "    model.fit(X_train_scaled, y_train)\n", "    \n", "    # Make predictions\n", "    y_pred = model.predict(X_test_scaled)\n", "    predictions[name] = y_pred\n", "    \n", "    # Calculate metrics\n", "    accuracy = accuracy_score(y_test, y_pred)\n", "    precision = precision_score(y_test, y_pred)\n", "    recall = recall_score(y_test, y_pred)\n", "    f1 = f1_score(y_test, y_pred)\n", "    \n", "    results[name] = {\n", "        'Accuracy': accuracy,\n", "        'Precision': precision,\n", "        'Recall': recall,\n", "        'F1-Score': f1\n", "    }\n", "    \n", "    print(f\"\\n{name} Results:\")\n", "    print(f\"Accuracy: {accuracy:.3f}\")\n", "    print(f\"Precision: {precision:.3f}\")\n", "    print(f\"Recall: {recall:.3f}\")\n", "    print(f\"F1-Score: {f1:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Model Evaluation (Confusion Matrix, Accuracy, Precision, Recall, F-beta)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Best Model: Logistic Regression with Accuracy: 0.735\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Find best model\n", "best_model = max(results.keys(), key=lambda x: results[x]['Accuracy'])\n", "print(f\"Best Model: {best_model} with Accuracy: {results[best_model]['Accuracy']:.3f}\")\n", "\n", "# Create confusion matrix visualization for best model\n", "plt.figure(figsize=(12, 4))\n", "\n", "# Confusion matrix\n", "plt.subplot(1, 3, 1)\n", "cm = confusion_matrix(y_test, predictions[best_model])\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')\n", "plt.title(f'Confusion Matrix - {best_model}')\n", "plt.ylabel('Actual')\n", "plt.xlabel('Predicted')\n", "\n", "# Model comparison\n", "plt.subplot(1, 3, 2)\n", "model_names = list(results.keys())\n", "accuracies = [results[model]['Accuracy'] for model in model_names]\n", "plt.bar(model_names, accuracies, color=['skyblue', 'lightgreen', 'lightcoral'])\n", "plt.title('Model Accuracy Comparison')\n", "plt.ylabel('Accuracy')\n", "plt.xticks(rotation=45)\n", "\n", "# F1-Score comparison\n", "plt.subplot(1, 3, 3)\n", "f1_scores = [results[model]['F1-Score'] for model in model_names]\n", "plt.bar(model_names, f1_scores, color=['skyblue', 'lightgreen', 'lightcoral'])\n", "plt.title('Model F1-Score Comparison')\n", "plt.y<PERSON><PERSON>('F1-Score')\n", "plt.xticks(rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Conclusion and Model Comparison"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "FINAL MODEL COMPARISON:\n", "==================================================\n", "                        Accuracy  Precision  Recall  F1-Score\n", "Logistic Regression        0.735      0.741   0.964     0.838\n", "Random Forest              0.735      0.765   0.904     0.829\n", "Support Vector Machine     0.709      0.709   1.000     0.830\n", "\n", "BEST MODEL: Logistic Regression\n", "Best Accuracy: 0.735\n", "\n", "CONCLUSION:\n", "- This project successfully demonstrates machine learning for liver disease prediction\n", "- The Logistic Regression achieved the highest accuracy of 73.5%\n", "- All models show reasonable performance for this medical classification task\n", "- The project aligns with the health/diagnosis problem domain as required\n"]}], "source": ["# Summary table\n", "results_df = pd.DataFrame(results).T\n", "print(\"\\nFINAL MODEL COMPARISON:\")\n", "print(\"=\" * 50)\n", "print(results_df.round(3))\n", "\n", "print(f\"\\nBEST MODEL: {best_model}\")\n", "print(f\"Best Accuracy: {results[best_model]['Accuracy']:.3f}\")\n", "\n", "print(\"\\nCONCLUSION:\")\n", "print(\"- This project successfully demonstrates machine learning for liver disease prediction\")\n", "print(f\"- The {best_model} achieved the highest accuracy of {results[best_model]['Accuracy']:.1%}\")\n", "print(\"- All models show reasonable performance for this medical classification task\")\n", "print(\"- The project aligns with the health/diagnosis problem domain as required\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}