#!/usr/bin/env python3
"""
Additional Visualizations for Liver Disease Prediction Report
=============================================================

This file generates the missing visualizations that should be included in the report.
These figures correspond to the [INSERT FIGURE X] placeholders in the report.

Author: <PERSON><PERSON><PERSON>
Student ID: 25123787
Course: Introduction to AI
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.metrics import confusion_matrix, accuracy_score, precision_score, recall_score, f1_score
import warnings
warnings.filterwarnings('ignore')

# Set style for better-looking plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Load the dataset
print("Loading dataset...")
df = pd.read_csv('liver_patient.csv')

# Data preprocessing
df['Gender'] = df['Gender'].map({'Male': 1, 'Female': 0})
df = df.dropna()

# Prepare features and target
X = df.drop('Dataset', axis=1)
y = df['Dataset'] - 1  # Convert to 0,1

# Split the data
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

# Scale the features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Train models for confusion matrix
models = {
    'Logistic Regression': LogisticRegression(random_state=42),
    'Random Forest': RandomForestClassifier(random_state=42, n_estimators=100),
    'Support Vector Machine': SVC(random_state=42)
}

results = {}
predictions = {}

for name, model in models.items():
    model.fit(X_train_scaled, y_train)
    y_pred = model.predict(X_test_scaled)
    predictions[name] = y_pred
    
    results[name] = {
        'Testing Accuracy': accuracy_score(y_test, y_pred),
        'Precision': precision_score(y_test, y_pred),
        'Recall': recall_score(y_test, y_pred),
        'F1-Score': f1_score(y_test, y_pred)
    }

# Find best model
best_model = max(results.keys(), key=lambda x: results[x]['Testing Accuracy'])

print(f"Generating visualizations for report...")
print(f"Best model: {best_model}")

# =============================================================================
# FIGURE 1: Correlation Matrix Heatmap
# =============================================================================
def generate_correlation_heatmap():
    """Generate correlation matrix heatmap"""
    plt.figure(figsize=(12, 10))
    
    # Calculate correlation matrix
    correlation_matrix = df.corr()
    
    # Create heatmap
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
    sns.heatmap(correlation_matrix, 
                mask=mask,
                annot=True, 
                cmap='RdYlBu_r', 
                center=0,
                square=True,
                fmt='.3f',
                cbar_kws={"shrink": .8})
    
    plt.title('Correlation Matrix Heatmap\nLiver Disease Dataset Features', 
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Features', fontsize=12)
    plt.ylabel('Features', fontsize=12)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    
    # Save the figure
    plt.savefig('figure1_correlation_heatmap.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ Figure 1: Correlation Matrix Heatmap saved as 'figure1_correlation_heatmap.png'")

# =============================================================================
# FIGURE 2: Box Plots for Key Biomarkers by Disease Status
# =============================================================================
def generate_box_plots():
    """Generate box plots for key biomarkers by disease status"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Box Plots: Key Biomarkers by Disease Status', fontsize=16, fontweight='bold')
    
    # Key features to plot
    key_features = ['Age', 'Total_Bilirubin', 'Direct_Bilirubin', 'Alkphos', 'Sgpt', 'Sgot']
    
    for i, feature in enumerate(key_features):
        row = i // 3
        col = i % 3
        
        # Create box plot
        sns.boxplot(data=df, x='Dataset', y=feature, ax=axes[row, col])
        axes[row, col].set_title(f'{feature} Distribution by Disease Status', fontweight='bold')
        axes[row, col].set_xlabel('Disease Status (1=Healthy, 2=Disease)')
        axes[row, col].set_ylabel(feature)
        
        # Add statistical annotation
        healthy_median = df[df['Dataset'] == 1][feature].median()
        disease_median = df[df['Dataset'] == 2][feature].median()
        axes[row, col].text(0.02, 0.98, f'Healthy Median: {healthy_median:.2f}', 
                           transform=axes[row, col].transAxes, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7))
        axes[row, col].text(0.02, 0.88, f'Disease Median: {disease_median:.2f}', 
                           transform=axes[row, col].transAxes, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('figure2_box_plots.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ Figure 2: Box Plots saved as 'figure2_box_plots.png'")

# =============================================================================
# FIGURE 3: Histograms of Key Features Distribution
# =============================================================================
def generate_histograms():
    """Generate histograms showing distribution of key features"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Feature Distributions by Disease Status', fontsize=16, fontweight='bold')
    
    key_features = ['Age', 'Total_Bilirubin', 'Direct_Bilirubin', 'Alkphos', 'Sgpt', 'Sgot']
    
    for i, feature in enumerate(key_features):
        row = i // 3
        col = i % 3
        
        # Plot histograms for both groups
        healthy_data = df[df['Dataset'] == 1][feature]
        disease_data = df[df['Dataset'] == 2][feature]
        
        axes[row, col].hist(healthy_data, alpha=0.7, label='Healthy', bins=30, color='skyblue')
        axes[row, col].hist(disease_data, alpha=0.7, label='Disease', bins=30, color='lightcoral')
        
        axes[row, col].set_title(f'{feature} Distribution', fontweight='bold')
        axes[row, col].set_xlabel(feature)
        axes[row, col].set_ylabel('Frequency')
        axes[row, col].legend()
        axes[row, col].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('figure3_histograms.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ Figure 3: Histograms saved as 'figure3_histograms.png'")

# =============================================================================
# FIGURE 4: Scatter Plot Matrix of Key Features
# =============================================================================
def generate_scatter_plots():
    """Generate scatter plot matrix of key features"""
    # Select key features for scatter plot matrix
    key_features = ['Age', 'Total_Bilirubin', 'Alkphos', 'Sgpt']
    scatter_data = df[key_features + ['Dataset']].copy()
    
    # Create scatter plot matrix
    fig, axes = plt.subplots(4, 4, figsize=(16, 16))
    fig.suptitle('Scatter Plot Matrix: Key Features with Disease Status', fontsize=16, fontweight='bold')
    
    for i in range(4):
        for j in range(4):
            if i == j:
                # Diagonal: histograms
                healthy_data = scatter_data[scatter_data['Dataset'] == 1][key_features[i]]
                disease_data = scatter_data[scatter_data['Dataset'] == 2][key_features[i]]
                axes[i, j].hist(healthy_data, alpha=0.7, label='Healthy', bins=20, color='skyblue')
                axes[i, j].hist(disease_data, alpha=0.7, label='Disease', bins=20, color='lightcoral')
                axes[i, j].set_title(f'{key_features[i]}')
                if i == 0:
                    axes[i, j].legend()
            else:
                # Off-diagonal: scatter plots
                healthy_data = scatter_data[scatter_data['Dataset'] == 1]
                disease_data = scatter_data[scatter_data['Dataset'] == 2]
                
                axes[i, j].scatter(healthy_data[key_features[j]], healthy_data[key_features[i]], 
                                 alpha=0.6, label='Healthy', color='skyblue', s=20)
                axes[i, j].scatter(disease_data[key_features[j]], disease_data[key_features[i]], 
                                 alpha=0.6, label='Disease', color='lightcoral', s=20)
                
                if i == 0 and j == 1:
                    axes[i, j].legend()
            
            # Set labels
            if i == 3:
                axes[i, j].set_xlabel(key_features[j])
            if j == 0:
                axes[i, j].set_ylabel(key_features[i])
            
            axes[i, j].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('figure4_scatter_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ Figure 4: Scatter Plot Matrix saved as 'figure4_scatter_matrix.png'")

# =============================================================================
# FIGURE 5: Model Performance Comparison Chart
# =============================================================================
def generate_performance_comparison():
    """Generate model performance comparison chart"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('Model Performance Comparison', fontsize=16, fontweight='bold')
    
    model_names = list(results.keys())
    metrics = ['Testing Accuracy', 'Precision', 'Recall', 'F1-Score']
    
    # Accuracy comparison
    accuracies = [results[model]['Testing Accuracy'] for model in model_names]
    bars1 = axes[0].bar(model_names, accuracies, color=['skyblue', 'lightgreen', 'lightcoral'])
    axes[0].set_title('Testing Accuracy Comparison', fontweight='bold')
    axes[0].set_ylabel('Accuracy')
    axes[0].set_ylim(0, 1)
    axes[0].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, acc in zip(bars1, accuracies):
        axes[0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # F1-Score comparison
    f1_scores = [results[model]['F1-Score'] for model in model_names]
    bars2 = axes[1].bar(model_names, f1_scores, color=['skyblue', 'lightgreen', 'lightcoral'])
    axes[1].set_title('F1-Score Comparison', fontweight='bold')
    axes[1].set_ylabel('F1-Score')
    axes[1].set_ylim(0, 1)
    axes[1].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, f1 in zip(bars2, f1_scores):
        axes[1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{f1:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # Comprehensive metrics comparison
    metrics_data = []
    for model in model_names:
        metrics_data.append([
            results[model]['Testing Accuracy'],
            results[model]['Precision'],
            results[model]['Recall'],
            results[model]['F1-Score']
        ])
    
    x = np.arange(len(model_names))
    width = 0.2
    
    for i, metric in enumerate(metrics):
        values = [metrics_data[j][i] for j in range(len(model_names))]
        axes[2].bar(x + i*width, values, width, label=metric, alpha=0.8)
    
    axes[2].set_title('All Metrics Comparison', fontweight='bold')
    axes[2].set_ylabel('Score')
    axes[2].set_xlabel('Models')
    axes[2].set_xticks(x + width * 1.5)
    axes[2].set_xticklabels(model_names)
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    axes[2].set_ylim(0, 1)
    
    # Rotate x-axis labels for better readability
    for ax in axes:
        ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('figure5_performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ Figure 5: Performance Comparison saved as 'figure5_performance_comparison.png'")

# =============================================================================
# FIGURE 6: Confusion Matrix for Best Performing Model
# =============================================================================
def generate_confusion_matrix():
    """Generate confusion matrix for the best performing model"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('Confusion Matrices for All Models', fontsize=16, fontweight='bold')
    
    for i, (model_name, model_predictions) in enumerate(predictions.items()):
        cm = confusion_matrix(y_test, model_predictions)
        
        # Create heatmap
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[i],
                   xticklabels=['Healthy', 'Disease'],
                   yticklabels=['Healthy', 'Disease'])
        
        axes[i].set_title(f'{model_name}\nAccuracy: {results[model_name]["Testing Accuracy"]:.3f}', 
                         fontweight='bold')
        axes[i].set_xlabel('Predicted')
        axes[i].set_ylabel('Actual')
        
        # Add performance metrics as text
        precision = results[model_name]['Precision']
        recall = results[model_name]['Recall']
        f1 = results[model_name]['F1-Score']
        
        axes[i].text(0.02, 0.98, f'Precision: {precision:.3f}\nRecall: {recall:.3f}\nF1-Score: {f1:.3f}', 
                    transform=axes[i].transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('figure6_confusion_matrices.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ Figure 6: Confusion Matrices saved as 'figure6_confusion_matrices.png'")

# =============================================================================
# MAIN EXECUTION
# =============================================================================
if __name__ == "__main__":
    print("="*70)
    print("GENERATING ADDITIONAL VISUALIZATIONS FOR LIVER DISEASE REPORT")
    print("="*70)
    
    # Generate all figures
    generate_correlation_heatmap()
    print()
    
    generate_box_plots()
    print()
    
    generate_histograms()
    print()
    
    generate_scatter_plots()
    print()
    
    generate_performance_comparison()
    print()
    
    generate_confusion_matrix()
    print()
    
    print("="*70)
    print("ALL VISUALIZATIONS GENERATED SUCCESSFULLY!")
    print("="*70)
    print("\nGenerated files:")
    print("📊 figure1_correlation_heatmap.png")
    print("📊 figure2_box_plots.png") 
    print("📊 figure3_histograms.png")
    print("📊 figure4_scatter_matrix.png")
    print("📊 figure5_performance_comparison.png")
    print("📊 figure6_confusion_matrices.png")
    print("\nThese figures correspond to the [INSERT FIGURE X] placeholders in your report.")
    print("Copy these images and insert them at the marked locations in your document.")
